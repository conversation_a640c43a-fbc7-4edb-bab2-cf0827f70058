import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { generateQRCodeImage, generateQRToken } from '@/utils/qr';
import { getZodiacFromDate } from '@/utils/zodiac';
import { getAdminFromRequest, requireAdminAuth } from '@/lib/auth';
import { ApiResponse, User, ZodiacSign, LanguageCode } from '@/types';

// Geocoding function to get coordinates from location name
async function geocodeLocation(location: string): Promise<{ latitude: number; longitude: number } | null> {
  try {
    // Using OpenStreetMap Nominatim API (free, no API key required)
    const encodedLocation = encodeURIComponent(location);
    const response = await fetch(
      `https://nominatim.openstreetmap.org/search?format=json&q=${encodedLocation}&limit=1`,
      {
        headers: {
          'User-Agent': 'AstroConnect/1.0 (astrology application)'
        }
      }
    );

    if (!response.ok) {
      throw new Error(`Geocoding API error: ${response.status}`);
    }

    const data = await response.json();

    if (data && data.length > 0) {
      return {
        latitude: parseFloat(data[0].lat),
        longitude: parseFloat(data[0].lon)
      };
    }

    return null;
  } catch (error) {
    console.error('Geocoding error:', error);
    return null;
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const admin = getAdminFromRequest(request);
    if (!requireAdminAuth(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 401 });
    }

    const requestBody = await request.json();
    console.log('Received user creation request:', requestBody);

    const {
      name,
      email,
      phoneNumber,
      address,
      birthDate,
      birthTime,
      birthPlace,
      zodiacSign,
      languagePreference
    } = requestBody;

    // Get default language from system settings if not provided
    let defaultLanguage = languagePreference;
    if (!defaultLanguage) {
      try {
        const settings = await prisma.systemSettings.findFirst();
        defaultLanguage = settings?.defaultLanguage || 'si';
      } catch (error) {
        console.error('Failed to get default language:', error);
        defaultLanguage = 'si';
      }
    }

    if (!name || !birthDate || !birthTime || !birthPlace) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Name, birth date, birth time, and birth place are required'
      }, { status: 400 });
    }

    // Validate birth date format
    const birthDateObj = new Date(birthDate);
    if (isNaN(birthDateObj.getTime())) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Invalid birth date format'
      }, { status: 400 });
    }

    // Use provided zodiac sign or calculate from birth date
    const finalZodiacSign = zodiacSign || getZodiacFromDate(birthDate) as ZodiacSign;

    // Validate zodiac sign
    if (!finalZodiacSign || !['aries', 'taurus', 'gemini', 'cancer', 'leo', 'virgo', 'libra', 'scorpio', 'sagittarius', 'capricorn', 'aquarius', 'pisces'].includes(finalZodiacSign)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Invalid zodiac sign'
      }, { status: 400 });
    }

    const qrToken = generateQRToken();
    console.log('Generated QR token:', qrToken);
    console.log('Final zodiac sign:', finalZodiacSign);
    console.log('Birth date object:', new Date(birthDate));

    // Test database connection first
    try {
      await prisma.$connect();
      console.log('Database connection successful');
    } catch (dbError) {
      console.error('Database connection failed:', dbError);
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: `Database connection failed: ${dbError instanceof Error ? dbError.message : 'Unknown error'}`
      }, { status: 500 });
    }

    // Get coordinates from birth place if provided
    let birthLatitude: number | null = null;
    let birthLongitude: number | null = null;

    if (birthPlace && birthPlace.trim()) {
      try {
        console.log('🌍 Geocoding birth place:', birthPlace);
        const coordinates = await geocodeLocation(birthPlace.trim());
        if (coordinates) {
          birthLatitude = coordinates.latitude;
          birthLongitude = coordinates.longitude;
          console.log('✅ Coordinates found:', coordinates);
        } else {
          console.log('⚠️ Could not geocode birth place, will use default coordinates');
          // Use Colombo, Sri Lanka as default
          birthLatitude = 6.9271;
          birthLongitude = 79.8612;
        }
      } catch (error) {
        console.error('❌ Geocoding error:', error);
        // Use Colombo, Sri Lanka as fallback
        birthLatitude = 6.9271;
        birthLongitude = 79.8612;
      }
    }

    // Prepare user data with proper null handling
    const userData = {
      name: name.trim(),
      email: email && email.trim() ? email.trim() : null,
      phoneNumber: phoneNumber && phoneNumber.trim() ? phoneNumber.trim() : null,
      address: address && address.trim() ? address.trim() : null,
      birthDate: new Date(birthDate),
      birthTime: birthTime && birthTime.trim() ? birthTime.trim() : null,
      birthPlace: birthPlace && birthPlace.trim() ? birthPlace.trim() : null,
      birthLatitude,
      birthLongitude,
      zodiacSign: finalZodiacSign,
      languagePreference: defaultLanguage as LanguageCode,
      qrToken
    };

    console.log('Creating user with data:', userData);

    // Validate all required fields one more time
    if (!userData.name || !userData.birthDate || !userData.zodiacSign || !userData.qrToken) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Missing required fields after validation'
      }, { status: 400 });
    }

    // Create user in database
    const user = await prisma.user.create({
      data: userData
    });

    console.log('User created successfully:', user.id);

    // Create QR code mapping
    console.log('Creating QR code mapping for user:', user.id);
    const qrMapping = await prisma.qrCodeMapping.create({
      data: {
        qrToken: qrToken,
        userId: user.id
      }
    });
    console.log('QR mapping created:', qrMapping.id);

    // Generate QR code image
    console.log('Generating QR code image...');
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    console.log('Using base URL for QR code:', baseUrl);
    const qrCodeImage = await generateQRCodeImage(qrToken, baseUrl);
    console.log('QR code image generated successfully');

    return NextResponse.json<ApiResponse<{ user: User; qrCodeImage: string }>>({
      success: true,
      data: {
        user: user as User,
        qrCodeImage
      },
      message: 'User created successfully with QR code'
    });

  } catch (error) {
    console.error('User creation error:', error);

    // Provide more detailed error information
    let errorMessage = 'Internal server error';
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });

      // Handle specific error types
      if (error.message.includes('Unique constraint')) {
        errorMessage = 'A user with this email or phone number already exists';
        statusCode = 409;
      } else if (error.message.includes('Invalid input')) {
        errorMessage = 'Invalid input data provided';
        statusCode = 400;
      }
    }

    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: `User creation failed: ${errorMessage}`
    }, { status: statusCode });
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const admin = getAdminFromRequest(request);
    if (!requireAdminAuth(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;
    // Get users with pagination
    const users = await prisma.user.findMany({
      include: {
        qrCodeMappings: {
          select: {
            scanCount: true,
            lastScanned: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip: offset,
      take: limit
    });

    // Get total count
    const total = await prisma.user.count();

    return NextResponse.json<ApiResponse<{ users: any[]; total: number; page: number; limit: number }>>({
      success: true,
      data: {
        users,
        total,
        page,
        limit
      },
      message: 'Users retrieved successfully'
    });

  } catch (error) {
    console.error('Users fetch error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check admin authentication
    const admin = getAdminFromRequest(request);
    if (!requireAdminAuth(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'User ID is required'
      }, { status: 400 });
    }
    await prisma.user.delete({
      where: { id: userId }
    });

    return NextResponse.json<ApiResponse<{ deleted: boolean }>>({
      success: true,
      data: { deleted: true },
      message: 'User deleted successfully'
    });

  } catch (error) {
    console.error('User deletion error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

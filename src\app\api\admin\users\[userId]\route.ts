import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getZodiacFromDate } from '@/utils/zodiac';
import { getAdminFromRequest, requireAdminAuth } from '@/lib/auth';
import { ApiResponse, User, ZodiacSign, LanguageCode } from '@/types';

// Geocoding function to get coordinates from location name
async function geocodeLocation(location: string): Promise<{ latitude: number; longitude: number } | null> {
  try {
    // Using OpenStreetMap Nominatim API (free, no API key required)
    const encodedLocation = encodeURIComponent(location);
    const response = await fetch(
      `https://nominatim.openstreetmap.org/search?format=json&q=${encodedLocation}&limit=1`,
      {
        headers: {
          'User-Agent': 'AstroConnect/1.0 (astrology application)'
        }
      }
    );

    if (!response.ok) {
      throw new Error(`Geocoding API error: ${response.status}`);
    }

    const data = await response.json();

    if (data && data.length > 0) {
      return {
        latitude: parseFloat(data[0].lat),
        longitude: parseFloat(data[0].lon)
      };
    }

    return null;
  } catch (error) {
    console.error('Geocoding error:', error);
    return null;
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    // Check admin authentication
    const admin = getAdminFromRequest(request);
    if (!requireAdminAuth(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 401 });
    }

    const { userId } = await params;
    if (!userId) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'User ID is required'
      }, { status: 400 });
    }

    const requestBody = await request.json();
    console.log('Received user update request for:', userId, requestBody);

    const {
      name,
      email,
      phoneNumber,
      address,
      birthDate,
      birthTime,
      birthPlace,
      zodiacSign,
      languagePreference
    } = requestBody;

    // Get default language from system settings if not provided
    let defaultLanguage = languagePreference;
    if (!defaultLanguage) {
      try {
        const settings = await prisma.systemSettings.findFirst();
        defaultLanguage = settings?.defaultLanguage || 'en';
      } catch (error) {
        console.error('Failed to get default language:', error);
        defaultLanguage = 'en';
      }
    }

    if (!name || !birthDate || !birthTime || !birthPlace) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Name, birth date, birth time, and birth place are required'
      }, { status: 400 });
    }

    // Validate birth date format
    const birthDateObj = new Date(birthDate);
    if (isNaN(birthDateObj.getTime())) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Invalid birth date format'
      }, { status: 400 });
    }

    // Use provided zodiac sign or calculate from birth date
    const finalZodiacSign = zodiacSign || getZodiacFromDate(birthDate) as ZodiacSign;

    // Validate zodiac sign
    if (!finalZodiacSign || !['aries', 'taurus', 'gemini', 'cancer', 'leo', 'virgo', 'libra', 'scorpio', 'sagittarius', 'capricorn', 'aquarius', 'pisces'].includes(finalZodiacSign)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Invalid zodiac sign'
      }, { status: 400 });
    }

    console.log('Final zodiac sign:', finalZodiacSign);
    console.log('Birth date object:', new Date(birthDate));

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!existingUser) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'User not found'
      }, { status: 404 });
    }

    // Get coordinates from birth place if provided and different from existing
    let birthLatitude: number | null = existingUser.birthLatitude;
    let birthLongitude: number | null = existingUser.birthLongitude;

    if (birthPlace && birthPlace.trim() && birthPlace.trim() !== existingUser.birthPlace) {
      try {
        console.log('🌍 Geocoding updated birth place:', birthPlace);
        const coordinates = await geocodeLocation(birthPlace.trim());
        if (coordinates) {
          birthLatitude = coordinates.latitude;
          birthLongitude = coordinates.longitude;
          console.log('✅ New coordinates found:', coordinates);
        } else {
          console.log('⚠️ Could not geocode birth place, keeping existing coordinates');
        }
      } catch (error) {
        console.error('❌ Geocoding error:', error);
        // Keep existing coordinates on error
      }
    }

    // Prepare user data with proper null handling
    const userData = {
      name: name.trim(),
      email: email && email.trim() ? email.trim() : null,
      phoneNumber: phoneNumber && phoneNumber.trim() ? phoneNumber.trim() : null,
      address: address && address.trim() ? address.trim() : null,
      birthDate: new Date(birthDate),
      birthTime: birthTime && birthTime.trim() ? birthTime.trim() : null,
      birthPlace: birthPlace && birthPlace.trim() ? birthPlace.trim() : null,
      birthLatitude,
      birthLongitude,
      zodiacSign: finalZodiacSign,
      languagePreference: defaultLanguage as LanguageCode,
      updatedAt: new Date()
    };

    console.log('Updating user with data:', userData);

    // Update user in database
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: userData
    });

    console.log('User updated successfully:', updatedUser.id);

    return NextResponse.json<ApiResponse<{ user: User }>>({
      success: true,
      data: {
        user: updatedUser as User
      },
      message: 'User updated successfully'
    });

  } catch (error) {
    console.error('User update error:', error);

    // Provide more detailed error information
    let errorMessage = 'Internal server error';
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });

      // Handle specific error types
      if (error.message.includes('Unique constraint')) {
        errorMessage = 'A user with this email or phone number already exists';
        statusCode = 409;
      } else if (error.message.includes('Invalid input')) {
        errorMessage = 'Invalid input data provided';
        statusCode = 400;
      } else if (error.message.includes('Record to update not found')) {
        errorMessage = 'User not found';
        statusCode = 404;
      }
    }

    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: `User update failed: ${errorMessage}`
    }, { status: statusCode });
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    // Check admin authentication
    const admin = getAdminFromRequest(request);
    if (!requireAdminAuth(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 401 });
    }

    const { userId } = await params;
    if (!userId) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'User ID is required'
      }, { status: 400 });
    }

    // Get user with QR code mappings
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        qrCodeMappings: {
          select: {
            scanCount: true,
            lastScanned: true,
            qrToken: true
          }
        }
      }
    });

    if (!user) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'User not found'
      }, { status: 404 });
    }

    return NextResponse.json<ApiResponse<{ user: any }>>({
      success: true,
      data: { user },
      message: 'User retrieved successfully'
    });

  } catch (error) {
    console.error('User fetch error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

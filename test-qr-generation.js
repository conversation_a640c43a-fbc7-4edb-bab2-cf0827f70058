// Test environment variable loading
require('dotenv').config();

console.log('🧪 Testing Environment Variables...\n');

console.log('Environment Variables:');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('NEXT_PUBLIC_APP_URL:', process.env.NEXT_PUBLIC_APP_URL);

// Test different dotenv configurations
console.log('\n🔍 Testing different environment file loading...');

// Load .env.production specifically
const dotenv = require('dotenv');
const path = require('path');

console.log('\nLoading .env.production:');
const prodResult = dotenv.config({ path: path.join(__dirname, '.env.production') });
if (prodResult.error) {
  console.error('Error loading .env.production:', prodResult.error);
} else {
  console.log('NEXT_PUBLIC_APP_URL from .env.production:', process.env.NEXT_PUBLIC_APP_URL);
}

console.log('\nLoading .env.local:');
const localResult = dotenv.config({ path: path.join(__dirname, '.env.local') });
if (localResult.error) {
  console.error('Error loading .env.local:', localResult.error);
} else {
  console.log('NEXT_PUBLIC_APP_URL after .env.local:', process.env.NEXT_PUBLIC_APP_URL);
}

console.log('\nFinal NEXT_PUBLIC_APP_URL:', process.env.NEXT_PUBLIC_APP_URL);

// Test URL generation logic
const testToken = 'test-token-123';
const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
const qrUrl = `${baseUrl}/qr/${testToken}`;

console.log('\n📱 QR URL Generation Test:');
console.log('Base URL:', baseUrl);
console.log('Generated QR URL:', qrUrl);
